<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Test de Diagnostic OpenAI ===\n\n";

// Test 1: Configuration
echo "1. Test de Configuration:\n";
echo "   - API Key configurée: " . (config('openai.api_key') ? 'OUI' : 'NON') . "\n";
echo "   - API Key longueur: " . strlen(config('openai.api_key')) . " caractères\n";
echo "   - Modèle configuré: " . config('openai.model', 'non défini') . "\n";
echo "   - Organization: " . (config('openai.organization') ?: 'non définie') . "\n\n";

// Test 2: Service OpenAI
echo "2. Test du Service OpenAI:\n";
try {
    $openAIService = $app->make(\App\Services\OpenAIService::class);
    echo "   - Service instancié: OUI\n";
    
    // Test simple de chat
    echo "   - Test de chat simple...\n";
    $messages = [
        ['role' => 'system', 'content' => 'Tu es un assistant utile.'],
        ['role' => 'user', 'content' => 'Dis bonjour en français.']
    ];
    
    $response = $openAIService->generateChatResponse($messages, ['max_tokens' => 50]);
    echo "   - Réponse reçue: " . (strlen($response) > 0 ? 'OUI' : 'NON') . "\n";
    echo "   - Contenu: " . substr($response, 0, 100) . "...\n\n";
    
} catch (\Exception $e) {
    echo "   - ERREUR: " . $e->getMessage() . "\n";
    echo "   - Type: " . get_class($e) . "\n";
    echo "   - Code: " . $e->getCode() . "\n\n";
}

// Test 3: Analyse de projet
echo "3. Test d'Analyse de Projet:\n";
try {
    $testData = [
        'project_type' => 'Site Web',
        'project_description' => 'Site e-commerce simple',
        'target_audience' => ['Particuliers'],
        'key_features' => ['Catalogue produits', 'Panier'],
        'budget_range' => '1000-5000€',
        'timeline' => '2 mois',
        'technical_requirements' => ['PHP', 'MySQL'],
        'needs_maintenance' => true
    ];
    
    $result = $openAIService->analyzeProjectRequirements($testData);
    echo "   - Analyse effectuée: " . (isset($result['error']) && $result['error'] ? 'NON' : 'OUI') . "\n";
    
    if (isset($result['error']) && $result['error']) {
        echo "   - Erreur: " . $result['message'] . "\n";
    } else {
        echo "   - Données reçues: " . count($result) . " éléments\n";
        echo "   - Clés: " . implode(', ', array_keys($result)) . "\n";
    }
    
} catch (\Exception $e) {
    echo "   - ERREUR: " . $e->getMessage() . "\n";
    echo "   - Type: " . get_class($e) . "\n\n";
}

// Test 4: Test direct de l'API OpenAI
echo "4. Test Direct de l'API OpenAI:\n";
try {
    $response = \OpenAI\Laravel\Facades\OpenAI::chat()->create([
        'model' => 'gpt-3.5-turbo',
        'messages' => [
            ['role' => 'user', 'content' => 'Hello, test message']
        ],
        'max_tokens' => 10
    ]);
    
    echo "   - API directe: OUI\n";
    echo "   - Réponse: " . ($response->choices[0]->message->content ?? 'Pas de contenu') . "\n";
    
} catch (\Exception $e) {
    echo "   - ERREUR API: " . $e->getMessage() . "\n";
    echo "   - Type: " . get_class($e) . "\n";
    echo "   - Code: " . $e->getCode() . "\n";
}

echo "\n=== Fin du Test ===\n";
